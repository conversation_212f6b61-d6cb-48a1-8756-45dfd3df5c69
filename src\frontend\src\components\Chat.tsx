import React, { useEffect, useState } from "react";
import { AgentPreview } from "./agents/AgentPreview";
import Layout from "./Layout";
import ChatSidebar from "./ChatSidebar";
import ProfileMenu from "./ProfileMenu";
import { SettingsPanel } from "./core/SettingsPanel";

const Chat: React.FC = () => {
  // State to store the agent details
  const [agentDetails, setAgentDetails] = useState({
    id: "loading",
    object: "agent",
    created_at: Date.now(),
    name: "Loading...",
    description: "Loading agent details...",
    model: "default",
    metadata: {
      logo: "robot",
    },
  });

  // State for sidebar
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // State for settings panel
  const [isSettingsPanelOpen, setIsSettingsPanelOpen] = useState(false);

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Fetch agent details when component mounts
  useEffect(() => {
    const fetchAgentDetails = async () => {
      try {
        const response = await fetch("/agent", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          console.log(
            "Agent details fetched successfully:",
            JSON.stringify(data)
          );
          console.log(
            "Agent details fetched successfully 2:",
            JSON.stringify(response)
          );
          setAgentDetails(data);
        } else {
          console.error("Failed to fetch agent details");
          // Set fallback data if fetch fails
          setAgentDetails({
            id: "fallback",
            object: "agent",
            created_at: Date.now(),
            name: "AI Agent",
            description: "Could not load agent details",
            model: "default",
            metadata: {
              logo: "robot",
            },
          });
        }
      } catch (error) {
        console.error("Error fetching agent details:", error);
        // Set fallback data if fetch fails
        setAgentDetails({
          id: "error",
          object: "agent",
          created_at: Date.now(),
          name: "AI Agent",
          description: "Error loading agent details",
          model: "default",
          metadata: {
            logo: "robot",
          },
        });
      }
    };

    fetchAgentDetails();
  }, []);

  // Menu items for profile dropdown
  const profileMenuItems = [
    {
      key: "settings",
      children: "Settings",
      onClick: () => {
        setIsSettingsPanelOpen(true);
      },
    },
    {
      key: "terms",
      children: (
        <a
          href="https://aka.ms/aistudio/terms"
          target="_blank"
          rel="noopener noreferrer"
          style={{ color: 'inherit', textDecoration: 'none' }}
        >
          Terms of Use
        </a>
      ),
    },
    {
      key: "logout",
      children: (
        <a
          href="/"
          style={{ color: 'inherit', textDecoration: 'none' }}
        >
          Logout
        </a>
      ),
    },
  ];

  return (
    <Layout currentPage="/chat" showNavBar={false}>
      <ChatSidebar
        isOpen={isSidebarOpen}
        onToggle={handleSidebarToggle}
      />
      <AgentPreview
        resourceId="sample-resource-id"
        agentDetails={agentDetails}
        onSidebarToggle={handleSidebarToggle}
      />

      {/* Profile Menu */}
      <ProfileMenu
        menuItems={profileMenuItems}
        alt="User Profile"
      />

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={isSettingsPanelOpen}
        onOpenChange={setIsSettingsPanelOpen}
      />
    </Layout>
  );
};

export default Chat;
